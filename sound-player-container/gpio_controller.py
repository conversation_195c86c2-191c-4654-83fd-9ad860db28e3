#!/usr/bin/env python3
"""
GPIO Controller for periodic relay switching.
"""

import gpiod
import gpiod.line
import time
import os
import sys
import logging
import threading

logger = logging.getLogger(__name__)

class GpioController:
    """Manages blinking a GPIO pin in a separate thread."""

    def __init__(self):
        # --- Configuration ---
        self.gpio_chip = os.getenv('GPIO_CHIP', 'gpiochip4')
        self.gpio_pin = int(os.environ.get('GPIO_PIN', '18'))
        self.blink_interval_s = float(os.getenv('BLINK_INTERVAL_S', 0.5))
        self.should_stop = threading.Event()
        self.thread = threading.Thread(target=self._run, name="GPIOController")

    def start(self):
        """Starts the GPIO blinking thread."""
        logger.info(f"Starting GPIO controller on chip '{self.gpio_chip}', pin {self.gpio_pin}...")
        self.thread.start()

    def stop(self):
        """Stops the GPIO blinking thread."""
        logger.info("Stopping GPIO controller...")
        self.should_stop.set()
        self.thread.join()

    def _run(self):
        """
        Main function to control the GPIO pin.
        It requests the GPIO line and blinks it until stopped.
        """
        try:
            with gpiod.request_lines(
                f'/dev/{self.gpio_chip}',
                consumer='sound-player-blinker',
                config={
                    self.gpio_pin: gpiod.LineSettings(
                        direction=gpiod.line.Direction.OUTPUT,
                        output_value=gpiod.line.Value.INACTIVE
                    )
                }
            ) as lines:
                logger.info("GPIO line requested successfully. Starting blink loop...")
                value = gpiod.line.Value.ACTIVE
                while not self.should_stop.is_set():
                    lines.set_values({self.gpio_pin: value})
                    logger.debug(f"Pin {self.gpio_pin} set to {'HIGH' if value == gpiod.line.Value.ACTIVE else 'LOW'}")

                    if value == gpiod.line.Value.ACTIVE:
                        value = gpiod.line.Value.INACTIVE
                    else:
                        value = gpiod.line.Value.ACTIVE

                    self.should_stop.wait(self.blink_interval_s)

        except FileNotFoundError:
            logger.error(f"GPIO chip '/dev/{self.gpio_chip}' not found. Ensure the container is run with the correct '--device' flag.")
        except Exception as e:
            logger.error(f"An unexpected error occurred in GPIO controller: {e}")