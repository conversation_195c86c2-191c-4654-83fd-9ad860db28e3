#!/usr/bin/env python3
"""
Audio Event Manager - Publisher-Subscriber system for audio events

This module provides a clean, extensible way to handle microphone input
and distribute audio events to multiple subscribers (click detection, 
recording, etc.) using the observer pattern.
"""

import logging
import queue
import threading
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import List, Optional, Callable, Any

import numpy as np
import sounddevice as sd
from scipy.signal import welch

logger = logging.getLogger(__name__)


@dataclass
class AudioEvent:
    """Container for audio event data and metadata."""
    chunk: np.ndarray
    timestamp: float
    sample_rate: int
    channels: int
    power_in_range: float
    freq_min_hz: float = 200
    freq_max_hz: float = 3500


class AudioEventSubscriber(ABC):
    """Abstract base class for audio event subscribers."""
    
    @abstractmethod
    def on_audio_chunk(self, event: AudioEvent) -> None:
        """Called when a new audio chunk is available."""
        pass
    
    @abstractmethod
    def on_start(self) -> None:
        """Called when audio processing starts."""
        pass
    
    @abstractmethod
    def on_stop(self) -> None:
        """Called when audio processing stops."""
        pass


class AudioEventManager:
    """
    Manages microphone input and distributes audio events to subscribers.
    
    This class handles the audio input stream, processes chunks, and notifies
    all registered subscribers of audio events using a thread-safe approach.
    """
    
    def __init__(self, 
                 sample_rate: int = 48000,
                 channels: int = 1,
                 chunk_size: int = 1024,
                 freq_min_hz: float = 200,
                 freq_max_hz: float = 3500):
        """
        Initialize the AudioEventManager.
        
        Args:
            sample_rate: Audio sample rate in Hz
            channels: Number of audio channels (1 for mono)
            chunk_size: Number of samples per chunk
            freq_min_hz: Minimum frequency for power analysis
            freq_max_hz: Maximum frequency for power analysis
        """
        self.sample_rate = sample_rate
        self.channels = channels
        self.chunk_size = chunk_size
        self.freq_min_hz = freq_min_hz
        self.freq_max_hz = freq_max_hz
        
        self._subscribers: List[AudioEventSubscriber] = []
        self._audio_queue = queue.Queue()
        self._audio_stream: Optional[sd.InputStream] = None
        self._processing_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
        self._input_device: Optional[Any] = None
        
        # Thread lock for subscriber management
        self._subscriber_lock = threading.Lock()
    
    def add_subscriber(self, subscriber: AudioEventSubscriber) -> None:
        """Add a subscriber to receive audio events."""
        with self._subscriber_lock:
            if subscriber not in self._subscribers:
                self._subscribers.append(subscriber)
                logger.info(f"Added audio event subscriber: {subscriber.__class__.__name__}")
    
    def remove_subscriber(self, subscriber: AudioEventSubscriber) -> None:
        """Remove a subscriber from receiving audio events."""
        with self._subscriber_lock:
            if subscriber in self._subscribers:
                self._subscribers.remove(subscriber)
                logger.info(f"Removed audio event subscriber: {subscriber.__class__.__name__}")
    
    def set_input_device(self, device: Any) -> None:
        """Set the audio input device to use."""
        self._input_device = device
    
    def _audio_callback(self, indata: np.ndarray, frames: int, time_info: Any, status: Any) -> None:
        """Callback function for the audio input stream."""
        if status:
            logger.warning(f"Audio callback status: {status}")
        
        # Put audio data in queue for processing
        self._audio_queue.put((indata.copy(), time.time()))
    
    def _calculate_power_in_range(self, chunk: np.ndarray) -> float:
        """Calculate power in the specified frequency range."""
        try:
            # Compute power spectral density
            frequencies, psd = welch(chunk, fs=self.sample_rate, nperseg=len(chunk))
            
            # Find indices for frequency range of interest
            freq_mask = (frequencies >= self.freq_min_hz) & (frequencies <= self.freq_max_hz)
            
            # Sum power in the frequency range
            power_in_range = np.sum(psd[freq_mask])
            
            return float(power_in_range)
        except Exception as e:
            logger.warning(f"Error calculating power spectrum: {e}")
            return 0.0
    
    def _process_audio_chunks(self) -> None:
        """Process audio chunks and notify subscribers."""
        logger.info("Audio processing thread started")
        
        while not self._stop_event.is_set():
            try:
                # Get audio chunk with timeout
                chunk_data, timestamp = self._audio_queue.get(timeout=0.1)
                
                # Convert to mono if needed
                if chunk_data.ndim > 1:
                    chunk_mono = chunk_data[:, 0]
                else:
                    chunk_mono = chunk_data
                
                # Calculate power in frequency range
                power_in_range = self._calculate_power_in_range(chunk_mono)
                
                # Create audio event
                event = AudioEvent(
                    chunk=chunk_mono,
                    timestamp=timestamp,
                    sample_rate=self.sample_rate,
                    channels=self.channels,
                    power_in_range=power_in_range,
                    freq_min_hz=self.freq_min_hz,
                    freq_max_hz=self.freq_max_hz
                )
                
                # Notify all subscribers
                with self._subscriber_lock:
                    for subscriber in self._subscribers:
                        try:
                            subscriber.on_audio_chunk(event)
                        except Exception as e:
                            logger.error(f"Error in subscriber {subscriber.__class__.__name__}: {e}")
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Error processing audio chunk: {e}")
        
        logger.info("Audio processing thread stopped")
    
    def start(self) -> bool:
        """Start audio processing."""
        if self._audio_stream is not None:
            logger.warning("Audio processing already started")
            return True
        
        if self._input_device is None:
            logger.error("No audio input device set")
            return False
        
        try:
            # Create audio input stream
            self._audio_stream = sd.InputStream(
                device=self._input_device,
                channels=self.channels,
                samplerate=self.sample_rate,
                blocksize=self.chunk_size,
                callback=self._audio_callback,
                dtype='int16'
            )
            
            # Start the stream
            self._audio_stream.start()
            
            # Start processing thread
            self._stop_event.clear()
            self._processing_thread = threading.Thread(target=self._process_audio_chunks, daemon=True)
            self._processing_thread.start()
            
            # Notify subscribers
            with self._subscriber_lock:
                for subscriber in self._subscribers:
                    try:
                        subscriber.on_start()
                    except Exception as e:
                        logger.error(f"Error in subscriber {subscriber.__class__.__name__} on_start: {e}")
            
            logger.info(f"Audio event manager started on device: {self._input_device}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start audio event manager: {e}")
            return False
    
    def stop(self) -> None:
        """Stop audio processing."""
        if self._audio_stream is None:
            return
        
        try:
            # Stop processing thread
            self._stop_event.set()
            if self._processing_thread and self._processing_thread.is_alive():
                self._processing_thread.join(timeout=2.0)
            
            # Stop audio stream
            self._audio_stream.stop()
            self._audio_stream.close()
            self._audio_stream = None
            
            # Notify subscribers
            with self._subscriber_lock:
                for subscriber in self._subscribers:
                    try:
                        subscriber.on_stop()
                    except Exception as e:
                        logger.error(f"Error in subscriber {subscriber.__class__.__name__} on_stop: {e}")
            
            logger.info("Audio event manager stopped")
            
        except Exception as e:
            logger.error(f"Error stopping audio event manager: {e}")
    
    def is_running(self) -> bool:
        """Check if audio processing is running."""
        return self._audio_stream is not None and self._audio_stream.active
    
    def get_subscriber_count(self) -> int:
        """Get the number of registered subscribers."""
        with self._subscriber_lock:
            return len(self._subscribers)
