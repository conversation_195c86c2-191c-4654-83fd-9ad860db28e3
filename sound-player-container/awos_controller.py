#!/usr/bin/env python3
"""AWOS Controller - Handles click sequence detection."""

import logging
import threading
import time
from typing import Optional, Callable

from unified_signal_detector import SignalEvent

logger = logging.getLogger(__name__)


class AWOSController:
    """Handles AWOS click sequence detection."""

    def __init__(self, awos_callback: Optional[Callable] = None,
                 weather_prep_callback: Optional[Callable] = None,
                 radio_check_callback: Optional[Callable] = None,
                 click_min_duration: float = 0.1, click_max_duration: float = 0.6,
                 click_cooldown: float = 1.0, awos_click_count: int = 3,
                 radio_check_click_count: int = 4):
        self.awos_callback = awos_callback
        self.weather_prep_callback = weather_prep_callback
        self.radio_check_callback = radio_check_callback
        self.click_min_duration = click_min_duration
        self.click_max_duration = click_max_duration
        self.click_cooldown = click_cooldown
        self.awos_click_count = awos_click_count
        self.radio_check_click_count = radio_check_click_count

        # State
        self.pending_click_sequence = 0
        self.last_valid_click_time: Optional[float] = None
        self.weather_preparation_started = False
        self._cooldown_timer: Optional[threading.Timer] = None
    
    def on_start(self) -> None:
        self.reset_click_sequence()

    def on_stop(self) -> None:
        self._cancel_cooldown_timer()
        self.reset_click_sequence()

    def on_signal_detected(self, signal_event: SignalEvent) -> None:
        """Handle signal for click sequence."""
        # Validate duration (signals < min already filtered by detector)
        if signal_event.duration > self.click_max_duration:
            if self.pending_click_sequence > 0:
                print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] Click sequence RESET due to long signal.")
                self.reset_click_sequence()
            return

        # Valid click
        self.pending_click_sequence += 1
        self.last_valid_click_time = signal_event.end_time

        # AWOS preparation at 3rd click
        if (self.pending_click_sequence == self.awos_click_count and
            not self.weather_preparation_started):
            self.weather_preparation_started = True
            if self.weather_prep_callback:
                self.weather_prep_callback()

        # Start cooldown timer
        self._start_cooldown_timer()
    
    def _finalize_click_sequence(self) -> None:
        """Finalize click sequence and trigger actions."""
        if self.pending_click_sequence == 0:
            return

        click_count = self.pending_click_sequence
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] Click sequence complete: {click_count} clicks")

        # Trigger action
        if click_count == self.awos_click_count and self.awos_callback:
            self.awos_callback()
        elif click_count == self.radio_check_click_count and self.radio_check_callback:
            self.radio_check_callback()

        self.reset_click_sequence()

    def reset_click_sequence(self) -> None:
        """Reset click sequence."""
        self._cancel_cooldown_timer()
        self.pending_click_sequence = 0
        self.last_valid_click_time = None
        self.weather_preparation_started = False

    def _start_cooldown_timer(self) -> None:
        """Start cooldown timer."""
        if self._cooldown_timer:
            self._cooldown_timer.cancel()
        self._cooldown_timer = threading.Timer(self.click_cooldown, self._finalize_click_sequence)
        self._cooldown_timer.start()

    def _cancel_cooldown_timer(self) -> None:
        """Cancel cooldown timer."""
        if self._cooldown_timer:
            self._cooldown_timer.cancel()
            self._cooldown_timer = None
