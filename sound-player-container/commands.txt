docker run -d --name better-awos-relay \
  -v /dev:/dev \
  --privileged \
  --restart unless-stopped \
  -e STATION_ID=185807 \
  -e TOKEN=04839500-dc95-4fed-8dbf-d02d90a0dd7c \
  -e AIRPORT_ADVISORY="Ridge Landing Airpark automated advisory" \
  -e TTS_VOICE="en-US-JennyNeural" \
  -e VOLUME=80 \
  -e WORD_GAP=-0.8 \
  -e PHRASE_GAP=0.2 \
  -e SAMPLE_RATE=48000 \
  -e CHANNELS=1 \
  -e CHUNK_SIZE=1024 \
  -e FREQ_MIN_HZ=200 \
  -e FREQ_MAX_HZ=3500 \
  -e SIGNAL_THRESHOLD_HIGH=10 \
  -e SIGNAL_THRESHOLD_LOW=8 \
  -e CLICK_MIN_DURATION=0.1 \
  -e CLICK_MAX_DURATION=0.6 \
  -e CLICK_COOLDOWN=1.0 \
  -e AWOS_CLICK_COUNT=3 \
  -e RADIO_CHECK_CLICK_COUNT=4 \
  -e RECORDING_STORAGE_PATH=/app/recordings \
  -e PRE_ROLL_SECONDS=0.5 \
  -e POST_ROLL_SECONDS=0.5 \
  -e MIN_SEGMENT_DURATION=0.1 \
  -e MAX_SEGMENT_DURATION=30.0 \
  -e MERGE_GAP_THRESHOLD=5.0 \
  -e STATION_TZ=Asia/Kuala_Lumpur \
  -e ENABLE_RECORDING=true \
  -e ENABLE_SIGNAL_DETECTION=true \
  -e ENABLE_S3_UPLOAD=true \
  -e S3_ACCESS_KEY_ID=c53df2e89e72fe035e7db5a899c9b4de \
  -e S3_SECRET_ACCESS_KEY=e08273037508b98ce57dc581b58db3b2c0bb1ddbbe1d32d28211cb1c83552ef6 \
  -e S3_ENDPOINT_URL=https://a80f24e552c9303eea94ad9ba226353d.r2.cloudflarestorage.com \
  -e S3_BUCKET_NAME=recordings \
  -e S3_REGION_NAME=auto \
  -e STATION_NAME=GRes \
  -e S3_MAX_RETRY_DELAY=300 \
  -e S3_MAX_CONCURRENT_UPLOADS=3 \
  -e GPIO_CHIP='gpiochip4' \
  -e GPIO_PIN=18 \
  -e BLINK_INTERVAL_S=0.5 \
  ghcr.io/devtomsuys/better-awos:latest